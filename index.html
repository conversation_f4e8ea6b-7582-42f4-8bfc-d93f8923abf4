<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حرب الفضاء 2D</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(to bottom, #000428, #004e92);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: 'Arial', sans-serif;
            color: white;
        }
        
        #gameContainer {
            text-align: center;
            position: relative;
        }
        
        #gameCanvas {
            border: 2px solid #00ffff;
            background: radial-gradient(ellipse at center, #001122 0%, #000000 100%);
            box-shadow: 0 0 20px #00ffff;
        }
        
        #gameInfo {
            margin-top: 10px;
            display: flex;
            justify-content: space-between;
            width: 800px;
            font-size: 18px;
            font-weight: bold;
        }
        
        #instructions {
            margin-top: 15px;
            font-size: 14px;
            color: #cccccc;
        }
        
        #gameOver {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            padding: 30px;
            border-radius: 10px;
            border: 2px solid #ff0000;
            display: none;
            text-align: center;
        }
        
        button {
            background: linear-gradient(45deg, #00ffff, #0080ff);
            border: none;
            color: white;
            padding: 10px 20px;
            font-size: 16px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 10px;
        }
        
        button:hover {
            background: linear-gradient(45deg, #0080ff, #00ffff);
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <h1>🚀 حرب الفضاء 2D 🚀</h1>
        <canvas id="gameCanvas" width="800" height="600"></canvas>
        <div id="gameInfo">
            <div>النقاط: <span id="score">0</span></div>
            <div>أعلى نقاط: <span id="highScore">0</span></div>
            <div>الأرواح: <span id="lives">3</span></div>
            <div>المستوى: <span id="level">1</span></div>
        </div>
        <div id="instructions">
            استخدم الأسهم للحركة • مسطرة المسافة للإطلاق • ESC للإيقاف المؤقت • M لتشغيل/إيقاف الصوت<br>
            الأسلحة: 1-عادي • 2-مزدوج • 3-منتشر • 4-ليزر
        </div>
        <div id="gameOver">
            <h2 id="gameOverTitle">انتهت اللعبة!</h2>
            <p>النقاط النهائية: <span id="finalScore">0</span></p>
            <p>المستوى المكتمل: <span id="finalLevel">1</span></p>
            <p id="newRecordMessage" style="color: #ffff00; display: none;">🎉 رقم قياسي جديد! 🎉</p>
            <div id="topScores" style="margin: 15px 0; max-height: 200px; overflow-y: auto;">
                <h3>أفضل النقاط:</h3>
                <div id="scoresList"></div>
            </div>
            <button onclick="restartGame()">العب مرة أخرى</button>
            <button onclick="toggleScores()" style="margin-left: 10px;">عرض/إخفاء النقاط</button>
        </div>
    </div>
    
    <script src="game.js"></script>
</body>
</html>
