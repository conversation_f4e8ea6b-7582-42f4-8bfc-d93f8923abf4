# 🚀 لعبة حرب الفضاء 2D

لعبة حرب الفضاء ثنائية الأبعاد مطورة بـ HTML5 Canvas و JavaScript

## 📋 وصف اللعبة

لعبة حرب الفضاء كلاسيكية حيث تتحكم في مركبة فضائية وتحارب الأعداء القادمين من الأعلى. الهدف هو تحقيق أعلى نقاط ممكنة والبقاء على قيد الحياة أطول فترة ممكنة.

## 🎮 كيفية اللعب

### التحكم:
- **الأسهم**: تحريك المركبة الفضائية (يسار، يمين، أعلى، أسفل)
- **مسطرة المسافة**: إطلاق النار
- **ESC**: إيقاف مؤقت / استئناف اللعبة

### قواعد اللعبة:
- لديك 3 أرواح في البداية
- كل عدو تقضي عليه يعطيك نقاط
- المستوى يزيد كل 500 نقطة
- سرعة الأعداء تزيد مع كل مستوى
- تخسر روحاً عند اصطدامك بعدو
- اللعبة تنتهي عند فقدان جميع الأرواح

### أنواع الأعداء:
- **العدو الأحمر**: 10 نقاط، سرعة متوسطة
- **العدو البرتقالي**: 20 نقطة، سرعة عالية
- **العدو الوردي**: 15 نقطة، سرعة بطيئة

## 🚀 كيفية تشغيل اللعبة

1. تأكد من وجود الملفات التالية في نفس المجلد:
   - `index.html`
   - `game.js`

2. افتح ملف `index.html` في متصفح الويب

3. ابدأ اللعب!

## 🛠️ الملفات

- **index.html**: الملف الرئيسي للعبة يحتوي على HTML و CSS
- **game.js**: منطق اللعبة والبرمجة
- **README.md**: هذا الملف

## ✨ المميزات

- رسوميات 2D جميلة مع تأثيرات بصرية
- نظام نقاط ومستويات
- تأثيرات انفجار
- خلفية نجوم متحركة
- واجهة مستخدم باللغة العربية
- تحكم سلس ومتجاوب
- نظام إيقاف مؤقت

## 🎯 التطويرات المستقبلية المقترحة

- إضافة أصوات وموسيقى
- أنواع أسلحة مختلفة
- عناصر قوة (Power-ups)
- أعداء أكثر تنوعاً
- مستويات مختلفة
- نظام حفظ أعلى النقاط

## 📱 التوافق

اللعبة تعمل على جميع المتصفحات الحديثة التي تدعم HTML5 Canvas:
- Chrome
- Firefox
- Safari
- Edge

---

استمتع باللعب! 🎮✨
