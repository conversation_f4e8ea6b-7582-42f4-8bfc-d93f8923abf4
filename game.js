// إعداد اللعبة
const canvas = document.getElementById('gameCanvas');
const ctx = canvas.getContext('2d');

// إعداد الصوت
let audioContext;
let soundEnabled = true;

// إنشاء الأصوات باستخدام Web Audio API
function createSound(frequency, duration, type = 'sine', volume = 0.1) {
    if (!audioContext || !soundEnabled) return;

    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
    oscillator.type = type;

    gainNode.gain.setValueAtTime(volume, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + duration);
}

// أصوات اللعبة
const sounds = {
    shoot: () => createSound(800, 0.1, 'square', 0.05),
    explosion: () => {
        createSound(150, 0.3, 'sawtooth', 0.1);
        setTimeout(() => createSound(100, 0.2, 'triangle', 0.08), 50);
    },
    enemyHit: () => createSound(300, 0.15, 'square', 0.06),
    powerUp: () => {
        createSound(523, 0.1, 'sine', 0.08);
        setTimeout(() => createSound(659, 0.1, 'sine', 0.08), 100);
        setTimeout(() => createSound(784, 0.1, 'sine', 0.08), 200);
    },
    gameOver: () => {
        createSound(200, 0.5, 'sawtooth', 0.1);
        setTimeout(() => createSound(150, 0.5, 'sawtooth', 0.1), 200);
        setTimeout(() => createSound(100, 1, 'sawtooth', 0.1), 400);
    }
};

// تشغيل الموسيقى الخلفية
let backgroundMusic = {
    playing: false,
    notes: [262, 294, 330, 349, 392, 440, 494, 523], // C major scale
    currentNote: 0,
    tempo: 500
};

function playBackgroundMusic() {
    if (!audioContext || !soundEnabled || !backgroundMusic.playing) return;

    const note = backgroundMusic.notes[backgroundMusic.currentNote];
    createSound(note, 0.3, 'sine', 0.02);

    backgroundMusic.currentNote = (backgroundMusic.currentNote + 1) % backgroundMusic.notes.length;

    setTimeout(playBackgroundMusic, backgroundMusic.tempo);
}

// تهيئة الصوت عند أول تفاعل
function initAudio() {
    if (!audioContext) {
        audioContext = new (window.AudioContext || window.webkitAudioContext)();
        backgroundMusic.playing = true;
        playBackgroundMusic();
    }
}

// متغيرات اللعبة
let gameState = 'playing'; // playing, paused, gameOver
let score = 0;
let lives = 3;
let level = 1;
let gameSpeed = 1;
let highScore = 0;

// نظام حفظ أعلى النقاط
function loadHighScore() {
    const saved = localStorage.getItem('spaceWarHighScore');
    if (saved) {
        highScore = parseInt(saved);
    }
}

function saveHighScore() {
    if (score > highScore) {
        highScore = score;
        localStorage.setItem('spaceWarHighScore', highScore.toString());
        return true; // رقم قياسي جديد
    }
    return false;
}

function getTopScores() {
    const scores = localStorage.getItem('spaceWarTopScores');
    if (scores) {
        return JSON.parse(scores);
    }
    return [];
}

function saveTopScore(newScore) {
    let topScores = getTopScores();
    topScores.push({
        score: newScore,
        date: new Date().toLocaleDateString('ar-SA'),
        level: level
    });

    // ترتيب النقاط من الأعلى للأقل
    topScores.sort((a, b) => b.score - a.score);

    // الاحتفاظ بأفضل 10 نقاط فقط
    topScores = topScores.slice(0, 10);

    localStorage.setItem('spaceWarTopScores', JSON.stringify(topScores));
}

// نظام الأسلحة
const weaponTypes = {
    NORMAL: {
        name: 'عادي',
        fireRate: 200,
        bulletSpeed: 7,
        bulletSize: { width: 4, height: 10 },
        color: '#ffff00',
        damage: 1,
        pattern: 'single'
    },
    DOUBLE: {
        name: 'مزدوج',
        fireRate: 150,
        bulletSpeed: 8,
        bulletSize: { width: 3, height: 12 },
        color: '#00ff00',
        damage: 1,
        pattern: 'double'
    },
    SPREAD: {
        name: 'منتشر',
        fireRate: 300,
        bulletSpeed: 6,
        bulletSize: { width: 3, height: 8 },
        color: '#ff8800',
        damage: 1,
        pattern: 'spread'
    },
    LASER: {
        name: 'ليزر',
        fireRate: 100,
        bulletSpeed: 12,
        bulletSize: { width: 2, height: 15 },
        color: '#ff0080',
        damage: 2,
        pattern: 'single'
    }
};

let currentWeapon = weaponTypes.NORMAL;
let lastFireTime = 0;
let weaponDuration = 0; // مدة السلاح المؤقت بالميلي ثانية

// كائن اللاعب
const player = {
    x: canvas.width / 2 - 25,
    y: canvas.height - 80,
    width: 50,
    height: 40,
    speed: 5,
    color: '#00ffff'
};

// مصفوفات الكائنات
let bullets = [];
let enemies = [];
let explosions = [];
let stars = [];
let powerUps = [];

// أنواع عناصر القوة
const powerUpTypes = {
    WEAPON_DOUBLE: {
        name: 'سلاح مزدوج',
        color: '#00ff00',
        effect: () => changeWeapon(weaponTypes.DOUBLE, 15000),
        symbol: '⚡'
    },
    WEAPON_SPREAD: {
        name: 'سلاح منتشر',
        color: '#ff8800',
        effect: () => changeWeapon(weaponTypes.SPREAD, 15000),
        symbol: '💥'
    },
    WEAPON_LASER: {
        name: 'ليزر',
        color: '#ff0080',
        effect: () => changeWeapon(weaponTypes.LASER, 20000),
        symbol: '🔥'
    },
    SHIELD: {
        name: 'درع',
        color: '#00ffff',
        effect: () => activateShield(10000),
        symbol: '🛡️'
    },
    EXTRA_LIFE: {
        name: 'حياة إضافية',
        color: '#ff0000',
        effect: () => { lives++; },
        symbol: '❤️'
    },
    SPEED_BOOST: {
        name: 'تسريع',
        color: '#ffff00',
        effect: () => activateSpeedBoost(8000),
        symbol: '⚡'
    }
};

// متغيرات عناصر القوة
let shieldActive = false;
let shieldDuration = 0;
let speedBoostActive = false;
let speedBoostDuration = 0;
let originalSpeed = 5;

// إعداد النجوم في الخلفية
function initStars() {
    for (let i = 0; i < 100; i++) {
        stars.push({
            x: Math.random() * canvas.width,
            y: Math.random() * canvas.height,
            size: Math.random() * 2,
            speed: Math.random() * 2 + 1
        });
    }
}

// رسم النجوم
function drawStars() {
    stars.forEach(star => {
        // تأثير وميض للنجوم
        const twinkle = Math.sin(Date.now() * 0.01 + star.x) * 0.5 + 0.5;
        const alpha = 0.3 + twinkle * 0.7;

        ctx.globalAlpha = alpha;
        ctx.fillStyle = star.size > 1.5 ? '#ffff88' : 'white';

        // رسم النجمة مع تأثير توهج خفيف
        if (star.size > 1.5) {
            ctx.shadowColor = '#ffff88';
            ctx.shadowBlur = 2;
        }

        ctx.fillRect(star.x, star.y, star.size, star.size);

        ctx.shadowBlur = 0;
        ctx.globalAlpha = 1;

        star.y += star.speed;
        if (star.y > canvas.height) {
            star.y = 0;
            star.x = Math.random() * canvas.width;
        }
    });
}

// رسم اللاعب
function drawPlayer() {
    // رسم الدرع إذا كان نشطاً
    if (shieldActive) {
        ctx.strokeStyle = '#00ffff';
        ctx.lineWidth = 3;
        ctx.shadowColor = '#00ffff';
        ctx.shadowBlur = 10;
        ctx.beginPath();
        ctx.arc(player.x + player.width / 2, player.y + player.height / 2, 35, 0, Math.PI * 2);
        ctx.stroke();
        ctx.shadowBlur = 0;
    }

    // رسم المركبة
    ctx.fillStyle = speedBoostActive ? '#ffff00' : player.color;
    ctx.fillRect(player.x, player.y, player.width, player.height);

    // رسم تفاصيل المركبة
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(player.x + 20, player.y - 5, 10, 15);
    ctx.fillStyle = '#ff0000';
    ctx.fillRect(player.x + 5, player.y + 35, 8, 8);
    ctx.fillRect(player.x + 37, player.y + 35, 8, 8);
}

// إنشاء رصاصة
function createBullet() {
    const now = Date.now();
    if (now - lastFireTime < currentWeapon.fireRate) return;

    lastFireTime = now;

    const centerX = player.x + player.width / 2;
    const bulletY = player.y;

    switch (currentWeapon.pattern) {
        case 'single':
            bullets.push(createSingleBullet(centerX, bulletY));
            break;
        case 'double':
            bullets.push(createSingleBullet(centerX - 10, bulletY));
            bullets.push(createSingleBullet(centerX + 10, bulletY));
            break;
        case 'spread':
            bullets.push(createSingleBullet(centerX, bulletY, 0));
            bullets.push(createSingleBullet(centerX - 5, bulletY, -1));
            bullets.push(createSingleBullet(centerX + 5, bulletY, 1));
            break;
    }

    // تشغيل صوت الإطلاق
    sounds.shoot();
}

function createSingleBullet(x, y, angle = 0) {
    return {
        x: x - currentWeapon.bulletSize.width / 2,
        y: y,
        width: currentWeapon.bulletSize.width,
        height: currentWeapon.bulletSize.height,
        speed: currentWeapon.bulletSpeed,
        color: currentWeapon.color,
        damage: currentWeapon.damage,
        angle: angle
    };
}

// رسم الرصاصات
function drawBullets() {
    bullets.forEach((bullet, index) => {
        ctx.fillStyle = bullet.color;

        // إضافة تأثير توهج للرصاصات
        ctx.shadowColor = bullet.color;
        ctx.shadowBlur = 8;

        // رسم الرصاصة مع تأثير الحركة
        ctx.fillRect(bullet.x, bullet.y, bullet.width, bullet.height);

        // إضافة أثر للرصاصات السريعة
        if (bullet.speed > 8) {
            ctx.globalAlpha = 0.5;
            ctx.fillRect(bullet.x, bullet.y + bullet.height, bullet.width, bullet.height);
            ctx.globalAlpha = 1;
        }

        ctx.shadowBlur = 0;

        // إنشاء جسيمات صغيرة خلف الرصاصة
        if (Math.random() < 0.3) {
            const particle = new Particle(
                bullet.x + bullet.width / 2,
                bullet.y + bullet.height,
                bullet.color,
                1,
                2,
                10
            );
            particle.vy = 1;
            particle.gravity = 0;
            particles.push(particle);
        }

        // تحديث موقع الرصاصة مع الزاوية
        bullet.y -= bullet.speed;
        if (bullet.angle) {
            bullet.x += bullet.angle * 2;
        }

        // إزالة الرصاصات التي خرجت من الشاشة
        if (bullet.y < 0 || bullet.x < 0 || bullet.x > canvas.width) {
            bullets.splice(index, 1);
        }
    });
}

// أنواع الأعداء المتقدمة
const advancedEnemyTypes = {
    BASIC: {
        width: 40, height: 30, color: '#ff0000', speed: 2, points: 10,
        health: 1, movement: 'straight', shootChance: 0
    },
    FAST: {
        width: 35, height: 35, color: '#ff8800', speed: 4, points: 20,
        health: 1, movement: 'straight', shootChance: 0
    },
    ZIGZAG: {
        width: 45, height: 25, color: '#ff00ff', speed: 1.5, points: 25,
        health: 1, movement: 'zigzag', shootChance: 0
    },
    SHOOTER: {
        width: 50, height: 35, color: '#8800ff', speed: 1, points: 30,
        health: 2, movement: 'straight', shootChance: 0.01
    },
    BOSS: {
        width: 80, height: 60, color: '#ff0080', speed: 0.5, points: 100,
        health: 5, movement: 'horizontal', shootChance: 0.02
    },
    SPIRAL: {
        width: 30, height: 30, color: '#00ff80', speed: 2, points: 35,
        health: 1, movement: 'spiral', shootChance: 0
    }
};

let enemyBullets = [];
let particles = [];

// نظام الجسيمات
class Particle {
    constructor(x, y, color, size = 2, speed = 3, life = 30) {
        this.x = x;
        this.y = y;
        this.vx = (Math.random() - 0.5) * speed;
        this.vy = (Math.random() - 0.5) * speed;
        this.color = color;
        this.size = size;
        this.life = life;
        this.maxLife = life;
        this.gravity = 0.1;
    }

    update() {
        this.x += this.vx;
        this.y += this.vy;
        this.vy += this.gravity;
        this.life--;

        // تقليل الحجم مع الوقت
        this.size = (this.life / this.maxLife) * this.size;
    }

    draw(ctx) {
        const alpha = this.life / this.maxLife;
        ctx.globalAlpha = alpha;
        ctx.fillStyle = this.color;
        ctx.shadowColor = this.color;
        ctx.shadowBlur = 5;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
        ctx.fill();
        ctx.shadowBlur = 0;
        ctx.globalAlpha = 1;
    }

    isDead() {
        return this.life <= 0;
    }
}

// إنشاء جسيمات الانفجار
function createExplosionParticles(x, y, count = 15, colors = ['#ff4444', '#ff8844', '#ffff44']) {
    for (let i = 0; i < count; i++) {
        const color = colors[Math.floor(Math.random() * colors.length)];
        const size = Math.random() * 4 + 2;
        const speed = Math.random() * 8 + 2;
        const life = Math.random() * 20 + 20;
        particles.push(new Particle(x, y, color, size, speed, life));
    }
}

// إنشاء جسيمات المحرك
function createEngineParticles() {
    if (gameState !== 'playing') return;

    // جسيمات من محرك المركبة
    const engineX = player.x + player.width / 2;
    const engineY = player.y + player.height;

    for (let i = 0; i < 3; i++) {
        const color = speedBoostActive ? '#ffff00' : '#0088ff';
        const size = Math.random() * 2 + 1;
        const speed = speedBoostActive ? 6 : 4;
        const life = 15;

        const particle = new Particle(engineX, engineY, color, size, speed, life);
        particle.vx = (Math.random() - 0.5) * 2;
        particle.vy = Math.random() * speed + 2;
        particle.gravity = 0;

        particles.push(particle);
    }
}

// تحديث ورسم الجسيمات
function updateAndDrawParticles() {
    particles.forEach((particle, index) => {
        particle.update();
        particle.draw(ctx);

        if (particle.isDead()) {
            particles.splice(index, 1);
        }
    });
}

// إنشاء عدو
function createEnemy() {
    const types = Object.values(advancedEnemyTypes);
    let type;

    // اختيار نوع العدو بناءً على المستوى
    if (level >= 5 && Math.random() < 0.1) {
        type = advancedEnemyTypes.BOSS;
    } else if (level >= 3 && Math.random() < 0.2) {
        type = advancedEnemyTypes.SHOOTER;
    } else if (level >= 2) {
        type = types[Math.floor(Math.random() * (types.length - 1))]; // استثناء البوس
    } else {
        type = types[Math.floor(Math.random() * 3)]; // الأنواع الأساسية فقط
    }

    enemies.push({
        x: Math.random() * (canvas.width - type.width),
        y: -type.height,
        width: type.width,
        height: type.height,
        speed: type.speed * gameSpeed,
        color: type.color,
        points: type.points,
        health: type.health,
        maxHealth: type.health,
        movement: type.movement,
        shootChance: type.shootChance,
        moveTimer: 0,
        direction: 1,
        angle: 0
    });
}

// تحديث حركة الأعداء
function updateEnemyMovement(enemy) {
    enemy.moveTimer++;

    switch (enemy.movement) {
        case 'straight':
            enemy.y += enemy.speed;
            break;

        case 'zigzag':
            enemy.y += enemy.speed;
            enemy.x += Math.sin(enemy.moveTimer * 0.1) * 2;
            break;

        case 'horizontal':
            enemy.y += enemy.speed * 0.5;
            enemy.x += enemy.direction * 2;
            if (enemy.x <= 0 || enemy.x >= canvas.width - enemy.width) {
                enemy.direction *= -1;
            }
            break;

        case 'spiral':
            enemy.y += enemy.speed;
            enemy.angle += 0.1;
            enemy.x += Math.cos(enemy.angle) * 3;
            break;
    }

    // إطلاق النار للأعداء المسلحين
    if (enemy.shootChance > 0 && Math.random() < enemy.shootChance) {
        createEnemyBullet(enemy);
    }
}

// إنشاء رصاصة عدو
function createEnemyBullet(enemy) {
    enemyBullets.push({
        x: enemy.x + enemy.width / 2 - 2,
        y: enemy.y + enemy.height,
        width: 4,
        height: 8,
        speed: 3,
        color: '#ff4444'
    });
}

// رسم رصاصات الأعداء
function drawEnemyBullets() {
    enemyBullets.forEach((bullet, index) => {
        ctx.fillStyle = bullet.color;
        ctx.shadowColor = bullet.color;
        ctx.shadowBlur = 3;
        ctx.fillRect(bullet.x, bullet.y, bullet.width, bullet.height);
        ctx.shadowBlur = 0;

        bullet.y += bullet.speed;

        // إزالة الرصاصات التي خرجت من الشاشة
        if (bullet.y > canvas.height) {
            enemyBullets.splice(index, 1);
        }
    });
}

// رسم الأعداء
function drawEnemies() {
    enemies.forEach((enemy, index) => {
        // رسم شريط الصحة للأعداء القوية
        if (enemy.maxHealth > 1) {
            const healthPercent = enemy.health / enemy.maxHealth;
            ctx.fillStyle = '#ff0000';
            ctx.fillRect(enemy.x, enemy.y - 8, enemy.width, 4);
            ctx.fillStyle = '#00ff00';
            ctx.fillRect(enemy.x, enemy.y - 8, enemy.width * healthPercent, 4);
        }

        // رسم العدو مع تأثير توهج
        ctx.fillStyle = enemy.color;
        ctx.shadowColor = enemy.color;
        ctx.shadowBlur = enemy.maxHealth > 1 ? 8 : 3;
        ctx.fillRect(enemy.x, enemy.y, enemy.width, enemy.height);
        ctx.shadowBlur = 0;

        // رسم تفاصيل العدو
        ctx.fillStyle = '#ffffff';
        const eyeSize = enemy.width > 50 ? 8 : 5;
        ctx.fillRect(enemy.x + 5, enemy.y + 5, eyeSize, eyeSize);
        ctx.fillRect(enemy.x + enemy.width - 5 - eyeSize, enemy.y + 5, eyeSize, eyeSize);

        // تحديث حركة العدو
        updateEnemyMovement(enemy);

        // إزالة الأعداء الذين خرجوا من الشاشة
        if (enemy.y > canvas.height || enemy.x < -enemy.width || enemy.x > canvas.width) {
            enemies.splice(index, 1);
        }
    });
}

// إنشاء انفجار
function createExplosion(x, y, size = 30, particleCount = 15) {
    explosions.push({
        x: x,
        y: y,
        size: 0,
        maxSize: size,
        life: 20
    });

    // إضافة جسيمات الانفجار
    createExplosionParticles(x, y, particleCount);
}

// رسم الانفجارات
function drawExplosions() {
    explosions.forEach((explosion, index) => {
        const alpha = explosion.life / 20;
        ctx.globalAlpha = alpha;
        
        ctx.fillStyle = `hsl(${Math.random() * 60}, 100%, 50%)`;
        ctx.beginPath();
        ctx.arc(explosion.x, explosion.y, explosion.size, 0, Math.PI * 2);
        ctx.fill();
        
        explosion.size += 2;
        explosion.life--;
        
        if (explosion.life <= 0) {
            explosions.splice(index, 1);
        }
    });
    ctx.globalAlpha = 1;
}

// فحص التصادمات
function checkCollisions() {
    // تصادم الرصاصات مع الأعداء
    bullets.forEach((bullet, bulletIndex) => {
        enemies.forEach((enemy, enemyIndex) => {
            if (bullet.x < enemy.x + enemy.width &&
                bullet.x + bullet.width > enemy.x &&
                bullet.y < enemy.y + enemy.height &&
                bullet.y + bullet.height > enemy.y) {

                // تقليل صحة العدو
                enemy.health -= bullet.damage || 1;

                // إزالة الرصاصة
                bullets.splice(bulletIndex, 1);

                // إذا مات العدو
                if (enemy.health <= 0) {
                    // إنشاء انفجار
                    createExplosion(enemy.x + enemy.width / 2, enemy.y + enemy.height / 2);

                    // تشغيل صوت الانفجار
                    sounds.enemyHit();

                    // زيادة النقاط
                    score += enemy.points;

                    // إزالة العدو
                    enemies.splice(enemyIndex, 1);

                    // فرصة لإسقاط عنصر قوة
                    if (Math.random() < 0.15) {
                        createPowerUp();
                    }
                } else {
                    // تأثير الإصابة
                    sounds.enemyHit();
                }
            }
        });
    });

    // تصادم رصاصات الأعداء مع اللاعب
    enemyBullets.forEach((bullet, index) => {
        if (bullet.x < player.x + player.width &&
            bullet.x + bullet.width > player.x &&
            bullet.y < player.y + player.height &&
            bullet.y + bullet.height > player.y) {

            // إزالة الرصاصة
            enemyBullets.splice(index, 1);

            // إذا لم يكن الدرع نشطاً
            if (!shieldActive) {
                // إنشاء انفجار
                createExplosion(player.x + player.width / 2, player.y + player.height / 2);

                // تشغيل صوت الانفجار
                sounds.explosion();

                // تقليل الأرواح
                lives--;

                // فحص انتهاء اللعبة
                if (lives <= 0) {
                    endGame();
                }
            }
        }
    });

    // تصادم اللاعب مع الأعداء
    enemies.forEach((enemy, index) => {
        if (player.x < enemy.x + enemy.width &&
            player.x + player.width > enemy.x &&
            player.y < enemy.y + enemy.height &&
            player.y + player.height > enemy.y) {

            // إنشاء انفجار
            createExplosion(player.x + player.width / 2, player.y + player.height / 2);

            // إزالة العدو
            enemies.splice(index, 1);

            // إذا لم يكن الدرع نشطاً، تقليل الأرواح
            if (!shieldActive) {
                // تشغيل صوت الانفجار
                sounds.explosion();

                // تقليل الأرواح
                lives--;

                // فحص انتهاء اللعبة
                if (lives <= 0) {
                    endGame();
                }
            } else {
                // إذا كان الدرع نشطاً، تشغيل صوت مختلف
                sounds.enemyHit();
            }
        }
    });

    // تصادم اللاعب مع عناصر القوة
    powerUps.forEach((powerUp, index) => {
        if (player.x < powerUp.x + powerUp.width &&
            player.x + player.width > powerUp.x &&
            player.y < powerUp.y + powerUp.height &&
            player.y + player.height > powerUp.y) {

            // تفعيل تأثير عنصر القوة
            powerUp.type.effect();

            // إزالة عنصر القوة
            powerUps.splice(index, 1);
        }
    });
}

// تحديث عناصر القوة
function updatePowerUps() {
    const now = Date.now();

    // تحديث السلاح
    if (weaponDuration > 0 && now > weaponDuration) {
        currentWeapon = weaponTypes.NORMAL;
        weaponDuration = 0;
    }

    // تحديث الدرع
    if (shieldActive && now > shieldDuration) {
        shieldActive = false;
        shieldDuration = 0;
    }

    // تحديث تسريع الحركة
    if (speedBoostActive && now > speedBoostDuration) {
        speedBoostActive = false;
        speedBoostDuration = 0;
        player.speed = originalSpeed;
    }
}

// تغيير السلاح مؤقتاً
function changeWeapon(weaponType, duration = 10000) {
    currentWeapon = weaponType;
    weaponDuration = Date.now() + duration;
    sounds.powerUp();
}

// تفعيل الدرع
function activateShield(duration) {
    shieldActive = true;
    shieldDuration = Date.now() + duration;
    sounds.powerUp();
}

// تفعيل تسريع الحركة
function activateSpeedBoost(duration) {
    speedBoostActive = true;
    speedBoostDuration = Date.now() + duration;
    player.speed = originalSpeed * 1.5;
    sounds.powerUp();
}

// إنشاء عنصر قوة
function createPowerUp() {
    const types = Object.values(powerUpTypes);
    const type = types[Math.floor(Math.random() * types.length)];

    powerUps.push({
        x: Math.random() * (canvas.width - 30),
        y: -30,
        width: 30,
        height: 30,
        speed: 2,
        type: type,
        rotation: 0
    });
}

// رسم عناصر القوة
function drawPowerUps() {
    powerUps.forEach((powerUp, index) => {
        ctx.save();

        // تأثير دوران
        powerUp.rotation += 0.1;
        ctx.translate(powerUp.x + powerUp.width / 2, powerUp.y + powerUp.height / 2);
        ctx.rotate(powerUp.rotation);

        // رسم الخلفية
        ctx.fillStyle = powerUp.type.color;
        ctx.shadowColor = powerUp.type.color;
        ctx.shadowBlur = 10;
        ctx.fillRect(-powerUp.width / 2, -powerUp.height / 2, powerUp.width, powerUp.height);

        // رسم الرمز
        ctx.fillStyle = 'white';
        ctx.font = '16px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(powerUp.type.symbol, 0, 5);

        ctx.restore();

        powerUp.y += powerUp.speed;

        // إزالة عناصر القوة التي خرجت من الشاشة
        if (powerUp.y > canvas.height) {
            powerUps.splice(index, 1);
        }
    });
}

// تحديث معلومات اللعبة
function updateGameInfo() {
    document.getElementById('score').textContent = score;
    document.getElementById('highScore').textContent = highScore;
    document.getElementById('lives').textContent = lives;
    document.getElementById('level').textContent = level;

    // زيادة المستوى كل 500 نقطة
    const newLevel = Math.floor(score / 500) + 1;
    if (newLevel > level) {
        level = newLevel;
        gameSpeed += 0.2;
    }
}

// عرض أفضل النقاط
function displayTopScores() {
    const topScores = getTopScores();
    const scoresList = document.getElementById('scoresList');

    if (topScores.length === 0) {
        scoresList.innerHTML = '<p style="color: #888;">لا توجد نقاط محفوظة بعد</p>';
        return;
    }

    let html = '<table style="width: 100%; color: white; font-size: 12px;">';
    html += '<tr><th>الترتيب</th><th>النقاط</th><th>المستوى</th><th>التاريخ</th></tr>';

    topScores.forEach((scoreData, index) => {
        const rank = index + 1;
        const medal = rank === 1 ? '🥇' : rank === 2 ? '🥈' : rank === 3 ? '🥉' : `${rank}.`;
        html += `<tr>
            <td>${medal}</td>
            <td>${scoreData.score}</td>
            <td>${scoreData.level}</td>
            <td>${scoreData.date}</td>
        </tr>`;
    });

    html += '</table>';
    scoresList.innerHTML = html;
}

// تبديل عرض النقاط
function toggleScores() {
    const topScores = document.getElementById('topScores');
    topScores.style.display = topScores.style.display === 'none' ? 'block' : 'none';
}

// انتهاء اللعبة
function endGame() {
    gameState = 'gameOver';
    backgroundMusic.playing = false;
    sounds.gameOver();

    // حفظ النقاط
    const isNewRecord = saveHighScore();
    saveTopScore(score);

    // تحديث واجهة انتهاء اللعبة
    document.getElementById('finalScore').textContent = score;
    document.getElementById('finalLevel').textContent = level;

    if (isNewRecord) {
        document.getElementById('newRecordMessage').style.display = 'block';
        document.getElementById('gameOverTitle').textContent = '🎉 رقم قياسي جديد! 🎉';
    } else {
        document.getElementById('newRecordMessage').style.display = 'none';
        document.getElementById('gameOverTitle').textContent = 'انتهت اللعبة!';
    }

    // عرض أفضل النقاط
    displayTopScores();

    document.getElementById('gameOver').style.display = 'block';
}

// رسم معلومات السلاح وعناصر القوة
function drawPowerUpInfo() {
    ctx.fillStyle = 'white';
    ctx.font = '14px Arial';
    let yPos = 30;

    // معلومات السلاح
    ctx.fillText(`السلاح: ${currentWeapon.name}`, 10, yPos);
    yPos += 20;

    if (weaponDuration > 0) {
        const timeLeft = Math.ceil((weaponDuration - Date.now()) / 1000);
        ctx.fillText(`الوقت المتبقي: ${timeLeft}s`, 10, yPos);
        yPos += 20;
    }

    // معلومات الدرع
    if (shieldActive) {
        const timeLeft = Math.ceil((shieldDuration - Date.now()) / 1000);
        ctx.fillStyle = '#00ffff';
        ctx.fillText(`🛡️ درع نشط: ${timeLeft}s`, 10, yPos);
        yPos += 20;
    }

    // معلومات التسريع
    if (speedBoostActive) {
        const timeLeft = Math.ceil((speedBoostDuration - Date.now()) / 1000);
        ctx.fillStyle = '#ffff00';
        ctx.fillText(`⚡ تسريع: ${timeLeft}s`, 10, yPos);
        yPos += 20;
    }
}

// حلقة اللعبة الرئيسية
function gameLoop() {
    if (gameState !== 'playing') return;

    // مسح الشاشة
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // تحديث عناصر القوة
    updatePowerUps();

    // إنشاء جسيمات المحرك
    if (Math.random() < 0.7) {
        createEngineParticles();
    }

    // رسم العناصر
    drawStars();
    updateAndDrawParticles();
    drawPlayer();
    drawBullets();
    drawEnemyBullets();
    drawEnemies();
    drawExplosions();
    drawPowerUps();
    drawPowerUpInfo();

    // فحص التصادمات
    checkCollisions();

    // تحديث معلومات اللعبة
    updateGameInfo();

    // إنشاء أعداء جدد
    if (Math.random() < 0.02 + (level * 0.005)) {
        createEnemy();
    }

    // إنشاء عناصر قوة جديدة (نادراً)
    if (Math.random() < 0.003) {
        createPowerUp();
    }

    requestAnimationFrame(gameLoop);
}

// التحكم بالمفاتيح
const keys = {};

document.addEventListener('keydown', (e) => {
    // تهيئة الصوت عند أول تفاعل
    initAudio();

    keys[e.key] = true;

    if (e.key === ' ') {
        e.preventDefault();
        if (gameState === 'playing') {
            createBullet();
        }
    }

    if (e.key === 'Escape') {
        e.preventDefault();
        if (gameState === 'playing') {
            gameState = 'paused';
            backgroundMusic.playing = false;
        } else if (gameState === 'paused') {
            gameState = 'playing';
            backgroundMusic.playing = true;
            playBackgroundMusic();
            gameLoop();
        }
    }

    // تشغيل/إيقاف الصوت بمفتاح M
    if (e.key === 'm' || e.key === 'M') {
        soundEnabled = !soundEnabled;
        if (!soundEnabled) {
            backgroundMusic.playing = false;
        } else if (gameState === 'playing') {
            backgroundMusic.playing = true;
            playBackgroundMusic();
        }
    }

    // تغيير الأسلحة بالأرقام (للاختبار)
    if (e.key === '1') changeWeapon(weaponTypes.NORMAL);
    if (e.key === '2') changeWeapon(weaponTypes.DOUBLE);
    if (e.key === '3') changeWeapon(weaponTypes.SPREAD);
    if (e.key === '4') changeWeapon(weaponTypes.LASER);
});

document.addEventListener('keyup', (e) => {
    keys[e.key] = false;
});

// تحديث حركة اللاعب
function updatePlayer() {
    if (gameState !== 'playing') return;

    if (keys['ArrowLeft'] && player.x > 0) {
        player.x -= player.speed;
    }
    if (keys['ArrowRight'] && player.x < canvas.width - player.width) {
        player.x += player.speed;
    }
    if (keys['ArrowUp'] && player.y > 0) {
        player.y -= player.speed;
    }
    if (keys['ArrowDown'] && player.y < canvas.height - player.height) {
        player.y += player.speed;
    }
}

// حلقة تحديث الحركة
function updateLoop() {
    updatePlayer();
    requestAnimationFrame(updateLoop);
}

// إعادة تشغيل اللعبة
function restartGame() {
    gameState = 'playing';
    score = 0;
    lives = 3;
    level = 1;
    gameSpeed = 1;
    bullets = [];
    enemies = [];
    enemyBullets = [];
    explosions = [];
    powerUps = [];
    player.x = canvas.width / 2 - 25;
    player.y = canvas.height - 80;

    // إعادة تعيين عناصر القوة
    currentWeapon = weaponTypes.NORMAL;
    weaponDuration = 0;
    shieldActive = false;
    shieldDuration = 0;
    speedBoostActive = false;
    speedBoostDuration = 0;
    player.speed = originalSpeed;

    // إعادة تشغيل الموسيقى
    if (soundEnabled) {
        backgroundMusic.playing = true;
        playBackgroundMusic();
    }

    document.getElementById('gameOver').style.display = 'none';
    gameLoop();
}

// جعل الوظائف متاحة عالمياً
window.restartGame = restartGame;
window.toggleScores = toggleScores;

// بدء اللعبة
loadHighScore();
initStars();
updateGameInfo(); // تحديث عرض أعلى النقاط
gameLoop();
updateLoop();
