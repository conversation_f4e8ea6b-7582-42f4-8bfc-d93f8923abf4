// إعداد اللعبة
const canvas = document.getElementById('gameCanvas');
const ctx = canvas.getContext('2d');

// متغيرات اللعبة
let gameState = 'playing'; // playing, paused, gameOver
let score = 0;
let lives = 3;
let level = 1;
let gameSpeed = 1;

// كائن اللاعب
const player = {
    x: canvas.width / 2 - 25,
    y: canvas.height - 80,
    width: 50,
    height: 40,
    speed: 5,
    color: '#00ffff'
};

// مصفوفات الكائنات
let bullets = [];
let enemies = [];
let explosions = [];
let stars = [];

// إعداد النجوم في الخلفية
function initStars() {
    for (let i = 0; i < 100; i++) {
        stars.push({
            x: Math.random() * canvas.width,
            y: Math.random() * canvas.height,
            size: Math.random() * 2,
            speed: Math.random() * 2 + 1
        });
    }
}

// رسم النجوم
function drawStars() {
    ctx.fillStyle = 'white';
    stars.forEach(star => {
        ctx.fillRect(star.x, star.y, star.size, star.size);
        star.y += star.speed;
        if (star.y > canvas.height) {
            star.y = 0;
            star.x = Math.random() * canvas.width;
        }
    });
}

// رسم اللاعب
function drawPlayer() {
    ctx.fillStyle = player.color;
    ctx.fillRect(player.x, player.y, player.width, player.height);
    
    // رسم تفاصيل المركبة
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(player.x + 20, player.y - 5, 10, 15);
    ctx.fillStyle = '#ff0000';
    ctx.fillRect(player.x + 5, player.y + 35, 8, 8);
    ctx.fillRect(player.x + 37, player.y + 35, 8, 8);
}

// إنشاء رصاصة
function createBullet() {
    bullets.push({
        x: player.x + player.width / 2 - 2,
        y: player.y,
        width: 4,
        height: 10,
        speed: 7,
        color: '#ffff00'
    });
}

// رسم الرصاصات
function drawBullets() {
    bullets.forEach((bullet, index) => {
        ctx.fillStyle = bullet.color;
        ctx.fillRect(bullet.x, bullet.y, bullet.width, bullet.height);
        bullet.y -= bullet.speed;
        
        // إزالة الرصاصات التي خرجت من الشاشة
        if (bullet.y < 0) {
            bullets.splice(index, 1);
        }
    });
}

// إنشاء عدو
function createEnemy() {
    const enemyTypes = [
        { width: 40, height: 30, color: '#ff0000', speed: 2, points: 10 },
        { width: 35, height: 35, color: '#ff8800', speed: 3, points: 20 },
        { width: 45, height: 25, color: '#ff00ff', speed: 1.5, points: 15 }
    ];
    
    const type = enemyTypes[Math.floor(Math.random() * enemyTypes.length)];
    
    enemies.push({
        x: Math.random() * (canvas.width - type.width),
        y: -type.height,
        width: type.width,
        height: type.height,
        speed: type.speed * gameSpeed,
        color: type.color,
        points: type.points
    });
}

// رسم الأعداء
function drawEnemies() {
    enemies.forEach((enemy, index) => {
        ctx.fillStyle = enemy.color;
        ctx.fillRect(enemy.x, enemy.y, enemy.width, enemy.height);
        
        // رسم تفاصيل العدو
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(enemy.x + 5, enemy.y + 5, 5, 5);
        ctx.fillRect(enemy.x + enemy.width - 10, enemy.y + 5, 5, 5);
        
        enemy.y += enemy.speed;
        
        // إزالة الأعداء الذين خرجوا من الشاشة
        if (enemy.y > canvas.height) {
            enemies.splice(index, 1);
        }
    });
}

// إنشاء انفجار
function createExplosion(x, y) {
    explosions.push({
        x: x,
        y: y,
        size: 0,
        maxSize: 30,
        life: 20
    });
}

// رسم الانفجارات
function drawExplosions() {
    explosions.forEach((explosion, index) => {
        const alpha = explosion.life / 20;
        ctx.globalAlpha = alpha;
        
        ctx.fillStyle = `hsl(${Math.random() * 60}, 100%, 50%)`;
        ctx.beginPath();
        ctx.arc(explosion.x, explosion.y, explosion.size, 0, Math.PI * 2);
        ctx.fill();
        
        explosion.size += 2;
        explosion.life--;
        
        if (explosion.life <= 0) {
            explosions.splice(index, 1);
        }
    });
    ctx.globalAlpha = 1;
}

// فحص التصادمات
function checkCollisions() {
    // تصادم الرصاصات مع الأعداء
    bullets.forEach((bullet, bulletIndex) => {
        enemies.forEach((enemy, enemyIndex) => {
            if (bullet.x < enemy.x + enemy.width &&
                bullet.x + bullet.width > enemy.x &&
                bullet.y < enemy.y + enemy.height &&
                bullet.y + bullet.height > enemy.y) {

                // إنشاء انفجار
                createExplosion(enemy.x + enemy.width / 2, enemy.y + enemy.height / 2);

                // زيادة النقاط
                score += enemy.points;

                // إزالة الرصاصة والعدو
                bullets.splice(bulletIndex, 1);
                enemies.splice(enemyIndex, 1);
            }
        });
    });

    // تصادم اللاعب مع الأعداء
    enemies.forEach((enemy, index) => {
        if (player.x < enemy.x + enemy.width &&
            player.x + player.width > enemy.x &&
            player.y < enemy.y + enemy.height &&
            player.y + player.height > enemy.y) {

            // إنشاء انفجار
            createExplosion(player.x + player.width / 2, player.y + player.height / 2);

            // تقليل الأرواح
            lives--;

            // إزالة العدو
            enemies.splice(index, 1);

            // فحص انتهاء اللعبة
            if (lives <= 0) {
                gameState = 'gameOver';
                document.getElementById('gameOver').style.display = 'block';
                document.getElementById('finalScore').textContent = score;
            }
        }
    });
}

// تحديث معلومات اللعبة
function updateGameInfo() {
    document.getElementById('score').textContent = score;
    document.getElementById('lives').textContent = lives;
    document.getElementById('level').textContent = level;

    // زيادة المستوى كل 500 نقطة
    const newLevel = Math.floor(score / 500) + 1;
    if (newLevel > level) {
        level = newLevel;
        gameSpeed += 0.2;
    }
}

// حلقة اللعبة الرئيسية
function gameLoop() {
    if (gameState !== 'playing') return;

    // مسح الشاشة
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // رسم العناصر
    drawStars();
    drawPlayer();
    drawBullets();
    drawEnemies();
    drawExplosions();

    // فحص التصادمات
    checkCollisions();

    // تحديث معلومات اللعبة
    updateGameInfo();

    // إنشاء أعداء جدد
    if (Math.random() < 0.02 + (level * 0.005)) {
        createEnemy();
    }

    requestAnimationFrame(gameLoop);
}

// التحكم بالمفاتيح
const keys = {};

document.addEventListener('keydown', (e) => {
    keys[e.key] = true;

    if (e.key === ' ') {
        e.preventDefault();
        if (gameState === 'playing') {
            createBullet();
        }
    }

    if (e.key === 'Escape') {
        e.preventDefault();
        if (gameState === 'playing') {
            gameState = 'paused';
        } else if (gameState === 'paused') {
            gameState = 'playing';
            gameLoop();
        }
    }
});

document.addEventListener('keyup', (e) => {
    keys[e.key] = false;
});

// تحديث حركة اللاعب
function updatePlayer() {
    if (gameState !== 'playing') return;

    if (keys['ArrowLeft'] && player.x > 0) {
        player.x -= player.speed;
    }
    if (keys['ArrowRight'] && player.x < canvas.width - player.width) {
        player.x += player.speed;
    }
    if (keys['ArrowUp'] && player.y > 0) {
        player.y -= player.speed;
    }
    if (keys['ArrowDown'] && player.y < canvas.height - player.height) {
        player.y += player.speed;
    }
}

// حلقة تحديث الحركة
function updateLoop() {
    updatePlayer();
    requestAnimationFrame(updateLoop);
}

// إعادة تشغيل اللعبة
function restartGame() {
    gameState = 'playing';
    score = 0;
    lives = 3;
    level = 1;
    gameSpeed = 1;
    bullets = [];
    enemies = [];
    explosions = [];
    player.x = canvas.width / 2 - 25;
    player.y = canvas.height - 80;

    document.getElementById('gameOver').style.display = 'none';
    gameLoop();
}

// بدء اللعبة
initStars();
gameLoop();
updateLoop();
